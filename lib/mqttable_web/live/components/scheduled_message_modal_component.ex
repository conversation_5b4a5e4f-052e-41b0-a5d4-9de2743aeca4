defmodule MqttableWeb.ScheduledMessageModalComponent do
  @moduledoc """
  A reusable modal component for creating and editing scheduled MQTT messages.

  This component provides a form for composing scheduled MQTT messages with support for:
  - Pre-filled and disabled client ID selection
  - Send interval configuration in milliseconds
  - All standard MQTT message properties (topic, payload, QoS, retain)
  - MQTT 5.0 properties support
  - Edit mode for existing scheduled messages
  """

  use MqttableWeb, :live_component
  import MqttableWeb.Shared.MessageFormComponents

  # Template support will be added in future iterations
  require Logger

  @impl true
  def mount(socket) do
    socket =
      socket
      |> assign_new(:show_modal, fn -> false end)
      |> assign_new(:scheduled_message_form, fn -> default_scheduled_message_form() end)
      |> assign_new(:edit_mode, fn -> false end)
      |> assign_new(:edit_index, fn -> nil end)
      |> assign_new(:alert_message, fn -> nil end)
      |> assign_new(:alert_type, fn -> nil end)
      |> assign_new(:uploaded_file, fn -> nil end)

    {:ok, socket}
  end

  @impl true
  def update(assigns, socket) do
    # Get connected clients for the active broker
    connected_clients = get_connected_clients(assigns[:active_broker_name] || "")

    # Load MQTT 5.0 properties collapse state from ui_state
    mqtt5_collapsed = get_mqtt5_properties_collapsed_state(assigns[:active_broker_name])

    # Initialize or update form based on edit mode and form state
    form =
      if assigns[:edit_mode] && assigns[:scheduled_message] do
        # Edit mode - populate form with existing scheduled message data
        # Normalize the scheduled message to handle both atom and string keys
        scheduled_msg = normalize_scheduled_message(assigns[:scheduled_message])

        # Calculate interval value and unit from interval_ms
        interval_ms = scheduled_msg.interval_ms || 5000
        interval_unit = determine_interval_unit(interval_ms)
        interval_value = calculate_interval_value(interval_ms, interval_unit)

        %{
          "client_id" => assigns[:pre_selected_client_id] || "",
          "topic" => scheduled_msg.topic || "",
          "payload" => scheduled_msg.payload || "",
          "payload_format" => scheduled_msg.payload_format || "text",
          "qos" => to_string(scheduled_msg.qos || 0),
          "retain" => scheduled_msg.retain || false,
          "interval_ms" => to_string(interval_ms),
          "interval_unit" => interval_unit,
          "interval_value" => to_string(interval_value),
          # MQTT 5.0 properties
          "content_type" => scheduled_msg.content_type || "",
          "payload_format_indicator" => scheduled_msg.payload_format_indicator || false,
          "message_expiry_interval" => scheduled_msg.message_expiry_interval || 0,
          "topic_alias" => scheduled_msg.topic_alias || 0,
          "response_topic" => scheduled_msg.response_topic || "",
          "correlation_data" => scheduled_msg.correlation_data || "",
          "user_properties" => scheduled_msg.user_properties || []
        }
      else
        # New message mode - use default form
        current_form = default_scheduled_message_form()

        # Set pre-selected client ID if provided
        if assigns[:pre_selected_client_id] do
          Map.put(current_form, "client_id", assigns[:pre_selected_client_id])
        else
          current_form
        end
      end

    # Simplified form handling - no template selection logic needed
    updated_form = form

    socket =
      socket
      |> assign(assigns)
      |> assign(:connected_clients, connected_clients)
      |> assign(:scheduled_message_form, updated_form)
      |> assign(:mqtt5_properties_collapsed, mqtt5_collapsed)

    {:ok, socket}
  end

  @impl true
  def render(assigns) do
    ~H"""
    <div class="modal modal-open">
      <div
        class="modal-box max-w-6xl ml-auto mr-6 mt-6 mb-6 h-[calc(100vh-3rem)] flex flex-col scheduled-message-modal-sidebar"
        id="scheduled-message-modal-content"
      >
        <!-- Modal Header -->
        <div class="flex items-center justify-between mb-4 flex-shrink-0">
          <h3 class="text-lg font-semibold flex items-center">
            <.icon name="hero-clock" class="size-5 mr-2" />
            <%= if @edit_mode do %>
              Edit Scheduled Message
            <% else %>
              New Scheduled Message
            <% end %>
          </h3>
          <button
            class="btn btn-sm btn-circle btn-ghost"
            phx-click="close_scheduled_message_modal"
            phx-target={@myself}
          >
            ✕
          </button>
        </div>
        
    <!-- Alert Message -->
        <div :if={@alert_message} class="mb-4 flex-shrink-0">
          <div
            role="alert"
            class={[
              "alert",
              @alert_type == :success && "alert-success",
              @alert_type == :error && "alert-error",
              @alert_type == :warning && "alert-warning"
            ]}
          >
            <svg
              :if={@alert_type == :success}
              xmlns="http://www.w3.org/2000/svg"
              class="h-6 w-6 shrink-0 stroke-current"
              fill="none"
              viewBox="0 0 24 24"
            >
              <path
                stroke-linecap="round"
                stroke-linejoin="round"
                stroke-width="2"
                d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"
              />
            </svg>
            <svg
              :if={@alert_type == :error}
              xmlns="http://www.w3.org/2000/svg"
              class="h-6 w-6 shrink-0 stroke-current"
              fill="none"
              viewBox="0 0 24 24"
            >
              <path
                stroke-linecap="round"
                stroke-linejoin="round"
                stroke-width="2"
                d="M10 14l2-2m0 0l2-2m-2 2l-2-2m2 2l2 2m7-2a9 9 0 11-18 0 9 9 0 0118 0z"
              />
            </svg>
            <svg
              :if={@alert_type == :warning}
              xmlns="http://www.w3.org/2000/svg"
              class="h-6 w-6 shrink-0 stroke-current"
              fill="none"
              viewBox="0 0 24 24"
            >
              <path
                stroke-linecap="round"
                stroke-linejoin="round"
                stroke-width="2"
                d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z"
              />
            </svg>
            <span>{@alert_message}</span>
            <button
              type="button"
              class="btn btn-sm btn-ghost ml-auto"
              phx-click="dismiss_alert"
              phx-target={@myself}
            >
              ✕
            </button>
          </div>
        </div>
        
    <!-- Modal Content with Sidebar Layout -->
        <div
          class="flex-1 overflow-hidden"
          phx-hook="PayloadEditor"
          id={"scheduled-payload-container-#{@myself}"}
        >
          <div class="h-full flex gap-6">
            <!-- Left Column: Main Form (60%) -->
            <div class="flex-1 overflow-y-auto pr-2">
              <.form
                for={%{}}
                as={:scheduled_message}
                phx-submit="save_scheduled_message"
                phx-change="form_changed"
                phx-target={@myself}
                class="space-y-4"
                id="scheduled-message-form"
              >
                <!-- Client ID Selection (disabled for scheduled messages) -->
                <.client_selection
                  form={@scheduled_message_form}
                  connected_clients={@connected_clients}
                  active_broker_name={@active_broker_name || ""}
                  myself={@myself}
                  disabled={true}
                  label="Client (Pre-selected)"
                  required={true}
                />
                
    <!-- Topic Input -->
                <div class="form-control w-full">
                  <label class="label">
                    <span class="label-text font-medium">
                      Topic <span class="text-error">*</span>
                    </span>
                  </label>
                  <label class="input input-bordered flex items-center gap-2">
                    <svg
                      width="16"
                      height="16"
                      viewBox="0 0 1024 1024"
                      version="1.1"
                      xmlns="http://www.w3.org/2000/svg"
                      p-id="14538"
                    >
                      <path
                        d="M512 85.333333c235.648 0 426.666667 191.018667 426.666667 426.666667s-191.018667 426.666667-426.666667 426.666667S85.333333 747.648 85.333333 512 276.352 85.333333 512 85.333333z m0 85.333334C323.477333 170.666667 170.666667 323.477333 170.666667 512s152.810667 341.333333 341.333333 341.333333 341.333333-152.810667 341.333333-341.333333S700.522667 170.666667 512 170.666667z m0 128c23.552 0 42.666667 19.114667 42.666667 42.666666v128h128c23.552 0 42.666667 19.114667 42.666667 42.666667s-19.114667 42.666667-42.666667 42.666667h-128v128c0 23.552-19.114667 42.666667-42.666667 42.666667s-42.666667-19.114667-42.666667-42.666667v-128h-128c-23.552 0-42.666667-19.114667-42.666667-42.666667s19.114667-42.666667 42.666667-42.666667h128v-128c0-23.552 19.114667-42.666667 42.666667-42.666667z"
                        fill="#172B4D"
                        p-id="14539"
                      >
                      </path>
                    </svg>
                    <input
                      type="text"
                      name="topic"
                      value={@scheduled_message_form["topic"]}
                      placeholder="Topic (e.g., 'sensor/temperature')"
                      class="grow"
                      required
                    />
                  </label>
                </div>
                
    <!-- Send Interval -->
                <div class="form-control w-full">
                  <label class="label">
                    <span class="label-text font-medium">
                      Send Interval <span class="text-error">*</span>
                    </span>
                  </label>
                  <div class="flex gap-2">
                    <input
                      type="number"
                      name="interval_value"
                      value={get_interval_value(@scheduled_message_form)}
                      min="1"
                      class="input input-bordered flex-1"
                      required
                    />
                    <select name="interval_unit" class="select select-bordered w-24">
                      <option value="ms" selected={@scheduled_message_form["interval_unit"] == "ms"}>
                        ms
                      </option>
                      <option value="s" selected={@scheduled_message_form["interval_unit"] == "s"}>
                        s
                      </option>
                      <option value="m" selected={@scheduled_message_form["interval_unit"] == "m"}>
                        m
                      </option>
                      <option value="h" selected={@scheduled_message_form["interval_unit"] == "h"}>
                        h
                      </option>
                    </select>
                  </div>
                </div>
                
    <!-- Payload Input with File Upload -->
                <.payload_input_with_file_upload
                  form={@scheduled_message_form}
                  myself={@myself}
                  uploaded_file={@uploaded_file}
                  label="Payload"
                />
                
    <!-- QoS Selection -->
                <.qos_selection form={@scheduled_message_form} myself={@myself} label="QoS Level" />
                
    <!-- Retain Checkbox -->
                <.retain_checkbox form={@scheduled_message_form} label="Retain Message" />
                
    <!-- MQTT 5.0 Properties Section -->
                <.mqtt5_properties_section
                  form={@scheduled_message_form}
                  myself={@myself}
                  collapsed={@mqtt5_properties_collapsed}
                  show_properties={
                    show_mqtt5_properties?(@scheduled_message_form["client_id"], @active_broker_name)
                  }
                />
                
    <!-- Submit Button -->
                <div class="form-control w-full">
                  <button type="submit" class="btn btn-primary">
                    <.icon name="hero-clock" class="size-4 mr-2" />
                    <%= if @edit_mode do %>
                      Update Scheduled Message
                    <% else %>
                      Create Scheduled Message
                    <% end %>
                  </button>
                </div>
              </.form>
            </div>
            
    <!-- Right Column: Template Helper Sidebar (40%) -->
            <div class="w-2/5 border-l border-base-300 pl-6 overflow-y-auto">
              <.live_component
                module={MqttableWeb.TwoTabTemplateHelperComponent}
                id={"two-tab-template-helper-scheduled-#{@myself}"}
                target_textarea_id={"enhanced-payload-editor-scheduled-#{@myself}"}
                payload={@scheduled_message_form["payload"] || ""}
                payload_format={@scheduled_message_form["payload_format"] || "text"}
                active_broker_name={@active_broker_name}
              />
            </div>
          </div>
        </div>
      </div>
      <div class="modal-backdrop" phx-click="close_scheduled_message_modal" phx-target={@myself}>
      </div>
    </div>
    """
  end

  # Event Handlers

  @impl true
  def handle_event("form_changed", params, socket) do
    # Update form state with all current values from the form
    updated_form = update_form_with_params(socket.assigns.scheduled_message_form, params)
    # Validate payload based on current format
    validated_form = validate_payload_in_form(updated_form)
    {:noreply, assign(socket, :scheduled_message_form, validated_form)}
  end

  @impl true
  def handle_event("client_selection_changed", %{"client_id" => client_id}, socket) do
    # This shouldn't happen since client selection is disabled, but handle it anyway
    updated_form = Map.put(socket.assigns.scheduled_message_form, "client_id", client_id)
    {:noreply, assign(socket, :scheduled_message_form, updated_form)}
  end

  # Template-related event handlers removed - using simplified payload editor now

  @impl true
  def handle_event("qos_changed", %{"qos" => qos}, socket) do
    updated_form = Map.put(socket.assigns.scheduled_message_form, "qos", qos)
    {:noreply, assign(socket, :scheduled_message_form, updated_form)}
  end

  @impl true
  def handle_event("toggle_mqtt5_properties", _params, socket) do
    current_state = socket.assigns.mqtt5_properties_collapsed
    new_state = !current_state

    # Save the new state to ui_state
    broker_name = socket.assigns[:active_broker_name]

    if broker_name do
      ui_state = Mqttable.ConnectionSets.get_ui_state()
      mqtt5_collapsed_states = Map.get(ui_state, :mqtt5_properties_collapsed, %{})
      updated_mqtt5_states = Map.put(mqtt5_collapsed_states, broker_name, new_state)
      updated_ui_state = Map.put(ui_state, :mqtt5_properties_collapsed, updated_mqtt5_states)
      Mqttable.ConnectionSets.update_ui_state(updated_ui_state)
    end

    {:noreply, assign(socket, :mqtt5_properties_collapsed, new_state)}
  end

  @impl true
  def handle_event("user_property_changed", params, socket) do
    # Extract user properties from the form parameters
    current_properties = socket.assigns.scheduled_message_form["user_properties"] || []

    # Parse the user property fields from params
    updated_properties = parse_user_properties_from_params(params, current_properties)

    # Preserve all existing form fields, only update user_properties
    updated_form =
      Map.put(socket.assigns.scheduled_message_form, "user_properties", updated_properties)

    {:noreply, assign(socket, :scheduled_message_form, updated_form)}
  end

  @impl true
  def handle_event("add_user_property", _params, socket) do
    current_properties = socket.assigns.scheduled_message_form["user_properties"] || []
    new_property = %{"key" => "", "value" => ""}
    updated_properties = current_properties ++ [new_property]

    updated_form =
      Map.put(socket.assigns.scheduled_message_form, "user_properties", updated_properties)

    {:noreply, assign(socket, :scheduled_message_form, updated_form)}
  end

  @impl true
  def handle_event("remove_user_property", %{"index" => index_str}, socket) do
    index = String.to_integer(index_str)
    current_properties = socket.assigns.scheduled_message_form["user_properties"] || []
    updated_properties = List.delete_at(current_properties, index)

    updated_form =
      Map.put(socket.assigns.scheduled_message_form, "user_properties", updated_properties)

    {:noreply, assign(socket, :scheduled_message_form, updated_form)}
  end

  @impl true
  def handle_event("save_scheduled_message", params, socket) do
    # Extract form parameters
    client_id = params["client_id"]
    topic = params["topic"]
    payload = params["payload"] || ""
    # Extract payload format from the correct field (either payload_format or format-scheduled-X)
    payload_format = extract_payload_format(params, socket.assigns.scheduled_message_form)
    qos = parse_integer_param(params["qos"], 0)
    retain = params["retain"] == "on"

    # Calculate interval in milliseconds
    interval_value = parse_integer_param(params["interval_value"], 5)
    interval_unit = params["interval_unit"] || "s"
    interval_ms = convert_to_milliseconds(interval_value, interval_unit)

    # Process payload and validate AFTER template evaluation
    {final_payload, payload_error} = process_payload(payload, socket.assigns[:active_broker_name])

    validated_form =
      validate_payload_in_form_after_template(
        %{"payload" => payload, "payload_format" => payload_format},
        final_payload,
        payload_format
      )

    # Validate required fields and payload format
    if client_id != "" && topic != "" && interval_ms > 0 &&
         validated_form["payload_validation_error"] == nil && payload_error == nil do
      # Create scheduled message data with MQTT 5 properties and template info
      scheduled_message = %{
        topic: topic,
        payload: payload,
        payload_format: payload_format,
        file_encoding: params["file_encoding"] || "binary",
        qos: qos,
        retain: retain,
        interval_ms: interval_ms,
        # MQTT 5.0 properties
        content_type: params["content_type"] || "",
        payload_format_indicator: params["payload_format_indicator"] == "on",
        message_expiry_interval: parse_integer_param(params["message_expiry_interval"], 0),
        topic_alias: parse_integer_param(params["topic_alias"], 0),
        response_topic: params["response_topic"] || "",
        correlation_data: params["correlation_data"] || "",
        user_properties: extract_user_properties_from_params(params)
      }

      # Send message to parent to save scheduled message first
      if socket.assigns.edit_mode do
        send(
          self(),
          {:update_scheduled_message, client_id, socket.assigns.edit_index, scheduled_message}
        )
      else
        send(self(), {:add_scheduled_message, client_id, scheduled_message})
      end

      Process.send(self(), {:close_scheduled_message_modal}, [])

      {:noreply, socket}
    else
      # Validation failed, show error message
      error_message =
        cond do
          client_id == "" ->
            "Please select a client"

          topic == "" ->
            "Please enter a topic"

          interval_ms <= 0 ->
            "Please enter a valid interval"

          payload_error != nil ->
            "Template error: #{payload_error}"

          validated_form["payload_validation_error"] != nil ->
            "Payload validation failed: #{validated_form["payload_validation_error"]}"

          true ->
            "Please fill in all required fields"
        end

      timestamped_message = add_timestamp_to_message(error_message)

      socket =
        socket
        |> assign(:alert_message, timestamped_message)
        |> assign(:alert_type, :error)

      {:noreply, socket}
    end
  end

  @impl true
  def handle_event("close_scheduled_message_modal", _params, socket) do
    # Send message to parent to close modal
    send(self(), {:close_scheduled_message_modal})
    {:noreply, socket}
  end

  @impl true
  def handle_event("dismiss_alert", _params, socket) do
    socket =
      socket
      |> assign(:alert_message, nil)
      |> assign(:alert_type, nil)

    {:noreply, socket}
  end

  @impl true
  def handle_event("clear_file", _params, socket) do
    socket =
      socket
      |> assign(:uploaded_file, nil)
      |> update(:scheduled_message_form, fn form ->
        form
        |> Map.put("payload", "")
        |> Map.put("payload_format", "text")
      end)

    {:noreply, socket}
  end

  @impl true
  def handle_event("format_changed", %{"format" => format}, socket) do
    socket =
      socket
      |> assign(:uploaded_file, nil)
      |> update(:scheduled_message_form, fn form ->
        form
        |> Map.put("payload_format", format)
        |> Map.put("payload", if(format == "file", do: "", else: form["payload"]))
        |> Map.put(
          "file_encoding",
          if(format == "file", do: form["file_encoding"] || "binary", else: nil)
        )
      end)

    {:noreply, socket}
  end

  @impl true
  def handle_event("file_encoding_changed", %{"encoding" => encoding}, socket) do
    socket =
      socket
      |> update(:scheduled_message_form, fn form ->
        Map.put(form, "file_encoding", encoding)
      end)

    {:noreply, socket}
  end

  @impl true
  def handle_event("validate", _params, socket) do
    # Just validate the form, don't process files yet
    {:noreply, socket}
  end

  # File upload is now handled directly in JavaScript, no LiveView events needed

  # Handle info messages

  # UI Components

  attr :form, :map, required: true
  attr :myself, :any, required: true
  attr :uploaded_file, :map, default: nil
  attr :label, :string, default: "Payload"

  def payload_input_with_file_upload(assigns) do
    ~H"""
    <fieldset class="fieldset">
      <legend class="fieldset-legend">{@label}</legend>
      
    <!-- Format Selection using DaisyUI Join -->
      <div class="join w-full mb-4">
        <input
          type="radio"
          name="payload_format"
          value="text"
          checked={@form["payload_format"] == "text" || @form["payload_format"] == nil}
          class="join-item btn btn-sm"
          aria-label="Text"
          phx-click="format_changed"
          phx-value-format="text"
          phx-target={@myself}
        />
        <input
          type="radio"
          name="payload_format"
          value="json"
          checked={@form["payload_format"] == "json"}
          class="join-item btn btn-sm"
          aria-label="JSON"
          phx-click="format_changed"
          phx-value-format="json"
          phx-target={@myself}
        />
        <input
          type="radio"
          name="payload_format"
          value="hex"
          checked={@form["payload_format"] == "hex"}
          class="join-item btn btn-sm"
          aria-label="Hex"
          phx-click="format_changed"
          phx-value-format="hex"
          phx-target={@myself}
        />
        <input
          type="radio"
          name="payload_format"
          value="file"
          checked={@form["payload_format"] == "file"}
          class="join-item btn btn-sm"
          aria-label="File"
          phx-click="format_changed"
          phx-value-format="file"
          phx-target={@myself}
        />
      </div>
      
    <!-- Content Area -->
      <%= if @form["payload_format"] == "file" do %>
        <!-- Hidden payload input for JavaScript to update -->
        <textarea name="payload" value={@form["payload"]} class="hidden"><%= @form["payload"] %></textarea>
        
    <!-- File Upload Card -->
        <div class="card bg-base-100 border border-base-300 shadow-sm">
          <div class="card-body p-4">
            <!-- File Encoding Selection -->
            <div class="mb-4">
              <div class="flex items-center gap-2 mb-2">
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  fill="none"
                  viewBox="0 0 24 24"
                  stroke-width="1.5"
                  stroke="currentColor"
                  class="size-4"
                >
                  <path
                    stroke-linecap="round"
                    stroke-linejoin="round"
                    d="M9.594 3.94c.09-.542.56-.94 1.11-.94h2.593c.55 0 1.02.398 1.11.94l.213 1.281c.063.374.313.686.645.*************.083.22.127.325.196.72.257 1.075.124l1.217-.456a1.125 1.125 0 0 1 1.37.49l1.296 2.247a1.125 1.125 0 0 1-.26 1.431l-1.003.827c-.293.241-.438.613-.43.992a6.759 6.759 0 0 1 0 .255c-.008.378.137.75.43.991l1.004.827c.424.35.534.955.26 1.43l-1.298 2.247a1.125 1.125 0 0 1-1.369.491l-1.217-.456c-.355-.133-.75-.072-1.076.124a6.57 6.57 0 0 1-.22.128c-.331.183-.581.495-.644.869l-.213 1.281c-.09.543-.56.94-1.11.94h-2.594c-.55 0-1.019-.398-1.11-.94l-.213-1.281c-.062-.374-.312-.686-.644-.87a6.52 6.52 0 0 1-.22-.127c-.325-.196-.72-.257-1.076-.124l-1.217.456a1.125 1.125 0 0 1-1.369-.49l-1.297-2.247a1.125 1.125 0 0 1 .26-1.431l1.004-.827c.292-.24.437-.613.43-.991a6.932 6.932 0 0 1 0-.255c.007-.38-.138-.751-.43-.992l-1.004-.827a1.125 1.125 0 0 1-.26-1.43l1.297-2.247a1.125 1.125 0 0 1 1.37-.491l1.216.456c.356.133.751.072 1.076-.124.072-.044.146-.086.22-.128.332-.183.582-.495.644-.869l.214-1.28Z"
                  />
                  <path
                    stroke-linecap="round"
                    stroke-linejoin="round"
                    d="M15 12a3 3 0 1 1-6 0 3 3 0 0 1 6 0Z"
                  />
                </svg>
                <span class="text-sm font-medium">Encoding</span>
              </div>

              <div class="join">
                <input
                  type="radio"
                  name="file_encoding"
                  value="binary"
                  checked={@form["file_encoding"] == "binary" || @form["file_encoding"] == nil}
                  class="join-item btn btn-sm btn-outline"
                  aria-label="Binary"
                  phx-click="file_encoding_changed"
                  phx-value-encoding="binary"
                  phx-target={@myself}
                />
                <input
                  type="radio"
                  name="file_encoding"
                  value="base64"
                  checked={@form["file_encoding"] == "base64"}
                  class="join-item btn btn-sm btn-outline"
                  aria-label="Base64"
                  phx-click="file_encoding_changed"
                  phx-value-encoding="base64"
                  phx-target={@myself}
                />
              </div>

              <div class="text-xs text-base-content/60 mt-2 flex items-center gap-1">
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  fill="none"
                  viewBox="0 0 24 24"
                  stroke-width="1.5"
                  stroke="currentColor"
                  class="size-3"
                >
                  <path
                    stroke-linecap="round"
                    stroke-linejoin="round"
                    d="m11.25 11.25.041-.02a.75.75 0 0 1 1.063.852l-.708 2.836a.75.75 0 0 0 1.063.853l.041-.021M21 12a9 9 0 1 1-18 0 9 9 0 0 1 18 0Zm-9-3.75h.008v.008H12V8.25Z"
                  />
                </svg>
                <%= if @form["file_encoding"] == "base64" do %>
                  Text-compatible format, larger size (~33% increase)
                <% else %>
                  Raw binary format, original file size
                <% end %>
              </div>
            </div>
            
    <!-- File Upload Area -->
            <%= if @uploaded_file do %>
              <!-- File Information Display -->
              <div class="bg-success/10 border border-success/20 rounded-lg p-4">
                <div class="flex items-center gap-2 text-success mb-3">
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    fill="none"
                    viewBox="0 0 24 24"
                    stroke-width="1.5"
                    stroke="currentColor"
                    class="size-5"
                  >
                    <path
                      stroke-linecap="round"
                      stroke-linejoin="round"
                      d="M9 12.75 11.25 15 15 9.75M21 12a9 9 0 1 1-18 0 9 9 0 0 1 18 0Z"
                    />
                  </svg>
                  <span class="font-medium">File uploaded successfully</span>
                </div>

                <div class="grid grid-cols-2 gap-2 text-sm mb-3">
                  <div><span class="text-base-content/60">Name:</span> {@uploaded_file.name}</div>
                  <div>
                    <span class="text-base-content/60">Size:</span> {format_file_size(
                      @uploaded_file.size
                    )}
                  </div>
                  <div><span class="text-base-content/60">Type:</span> {@uploaded_file.type}</div>
                  <%= if @form["file_encoding"] == "base64" do %>
                    <div>
                      <span class="text-base-content/60">Encoded:</span> {format_file_size(
                        calculate_base64_size(@uploaded_file.size)
                      )}
                    </div>
                  <% end %>
                </div>

                <button
                  type="button"
                  class="btn btn-sm btn-outline btn-error"
                  phx-click="clear_file"
                  phx-target={@myself}
                >
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    fill="none"
                    viewBox="0 0 24 24"
                    stroke-width="1.5"
                    stroke="currentColor"
                    class="size-4 mr-1"
                  >
                    <path stroke-linecap="round" stroke-linejoin="round" d="M6 18 18 6M6 6l12 12" />
                  </svg>
                  Clear File
                </button>
              </div>
            <% else %>
              <!-- File Upload Input -->
              <div class="border-2 border-dashed border-base-300 rounded-lg p-6 text-center hover:border-primary/50 transition-colors">
                <div class="flex flex-col items-center gap-3">
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    fill="none"
                    viewBox="0 0 24 24"
                    stroke-width="1.5"
                    stroke="currentColor"
                    class="size-8 text-base-content/40"
                  >
                    <path
                      stroke-linecap="round"
                      stroke-linejoin="round"
                      d="M3 16.5v2.25A2.25 2.25 0 0 0 5.25 21h13.5A2.25 2.25 0 0 0 21 18.75V16.5m-13.5-9L12 3m0 0 4.5 4.5M12 3v13.5"
                    />
                  </svg>
                  <div>
                    <p class="text-sm font-medium mb-1">Choose a file to upload</p>
                    <p class="text-xs text-base-content/60">Maximum size: 16MB</p>
                  </div>
                  <input
                    type="file"
                    class="file-input file-input-bordered file-input-primary file-input-sm w-full max-w-xs"
                    id="scheduled-file-upload-input"
                    phx-hook="FileUpload"
                    phx-target={@myself}
                  />
                </div>
              </div>
            <% end %>
          </div>
        </div>
      <% else %>
        <!-- Text/JSON/Hex Input -->
        <textarea
          name="payload"
          value={@form["payload"]}
          placeholder={get_payload_placeholder(@form["payload_format"])}
          class="textarea textarea-bordered w-full min-h-[120px]"
          spellcheck="false"
        ><%= @form["payload"] %></textarea>
      <% end %>
      
    <!-- Validation Error -->
      <%= if @form["payload_validation_error"] do %>
        <div class="mt-2">
          <div class="alert alert-error alert-sm">
            <svg
              xmlns="http://www.w3.org/2000/svg"
              fill="none"
              viewBox="0 0 24 24"
              stroke-width="1.5"
              stroke="currentColor"
              class="size-4"
            >
              <path
                stroke-linecap="round"
                stroke-linejoin="round"
                d="M12 9v3.75m9-.75a9 9 0 1 1-18 0 9 9 0 0 1 18 0Zm-9 3.75h.008v.008H12v-.008Z"
              />
            </svg>
            <span>{@form["payload_validation_error"]}</span>
          </div>
        </div>
      <% end %>
    </fieldset>
    """
  end

  # Helper functions

  defp get_payload_placeholder(format) do
    case format do
      "json" -> "Enter JSON payload..."
      "hex" -> "Enter hex payload (e.g., 48656c6c6f)"
      "file" -> ""
      _ -> "Enter payload..."
    end
  end

  defp format_file_size(size) when is_integer(size) do
    cond do
      size >= 1_048_576 -> "#{Float.round(size / 1_048_576, 1)} MB"
      size >= 1024 -> "#{Float.round(size / 1024, 1)} KB"
      true -> "#{size} B"
    end
  end

  defp format_file_size(_), do: "Unknown size"

  defp calculate_base64_size(original_size) when is_integer(original_size) do
    # Base64 encoding increases size by approximately 4/3 (33%)
    # Plus padding for alignment
    trunc(original_size * 4 / 3) + 4
  end

  defp calculate_base64_size(_), do: 0

  defp show_mqtt5_properties?(client_id, active_broker_name) do
    # Show MQTT 5.0 properties if client supports MQTT 5.0
    if client_id != "" do
      connected_clients = get_connected_clients(active_broker_name || "")

      case Enum.find(connected_clients, fn client -> client.client_id == client_id end) do
        %{mqtt_version: version} when version in ["5.0", "5"] -> true
        _ -> false
      end
    else
      false
    end
  end

  defp default_scheduled_message_form do
    %{
      "client_id" => "",
      "topic" => "",
      "payload" => "",
      "payload_format" => "text",
      "payload_validation_error" => nil,
      "qos" => "0",
      "retain" => false,
      "interval_ms" => "5000",
      "interval_unit" => "s",
      "interval_value" => "5",
      # MQTT 5.0 properties - use proper data types
      "content_type" => "",
      "payload_format_indicator" => false,
      "message_expiry_interval" => 0,
      "topic_alias" => 0,
      "response_topic" => "",
      "correlation_data" => "",
      "user_properties" => []
    }
  end

  defp update_form_with_params(current_form, params) do
    # Handle both nested (scheduled_message) and direct parameters
    form_params = params["scheduled_message"] || params

    # Update form with new parameters, handling special cases for MQTT 5 properties
    updated_form =
      Enum.reduce(form_params, current_form, fn {key, value}, acc ->
        case key do
          "message_expiry_interval" ->
            # Convert to integer, default to 0 if empty or invalid
            case value do
              "" ->
                Map.put(acc, key, 0)

              val when is_binary(val) ->
                case Integer.parse(val) do
                  {int_val, ""} when int_val >= 0 and int_val <= 4_294_967_295 ->
                    Map.put(acc, key, int_val)

                  _ ->
                    Map.put(acc, key, 0)
                end

              val when is_integer(val) ->
                Map.put(acc, key, val)

              _ ->
                Map.put(acc, key, 0)
            end

          "topic_alias" ->
            # Convert to integer, default to 0 if empty or invalid
            case value do
              "" ->
                Map.put(acc, key, 0)

              val when is_binary(val) ->
                case Integer.parse(val) do
                  {int_val, ""} when int_val >= 0 and int_val <= 65535 ->
                    Map.put(acc, key, int_val)

                  _ ->
                    Map.put(acc, key, 0)
                end

              val when is_integer(val) ->
                Map.put(acc, key, val)

              _ ->
                Map.put(acc, key, 0)
            end

          # Skip internal Phoenix form fields
          key
          when key in [
                 "_target",
                 "_unused_content_type",
                 "_unused_correlation_data",
                 "_unused_interval_unit",
                 "_unused_interval_value",
                 "_unused_message_expiry_interval",
                 "_unused_response_topic",
                 "_unused_retain",
                 "_unused_topic",
                 "_unused_topic_alias"
               ] ->
            acc

          _ ->
            Map.put(acc, key, value)
        end
      end)

    # Handle user properties separately if they exist in params
    user_properties = extract_user_properties_from_params(params)

    if length(user_properties) > 0 do
      Map.put(updated_form, "user_properties", user_properties)
    else
      updated_form
    end
  end

  defp get_connected_clients(broker_name) do
    case find_connection_set_by_name(Mqttable.ConnectionSets.get_all(), broker_name) do
      nil ->
        []

      connection_set ->
        connection_set.connections
        |> Enum.filter(fn conn -> Map.get(conn, :status) == "connected" end)
    end
  end

  defp find_connection_set_by_name(connection_sets, name) do
    Enum.find(connection_sets, fn set -> set.name == name end)
  end

  defp determine_interval_unit(interval_ms) when is_integer(interval_ms) do
    cond do
      rem(interval_ms, 3_600_000) == 0 -> "h"
      rem(interval_ms, 60_000) == 0 -> "m"
      rem(interval_ms, 1000) == 0 -> "s"
      true -> "ms"
    end
  end

  defp calculate_interval_value(interval_ms, unit) when is_integer(interval_ms) do
    case unit do
      "h" -> div(interval_ms, 3_600_000)
      "m" -> div(interval_ms, 60_000)
      "s" -> div(interval_ms, 1000)
      "ms" -> interval_ms
    end
  end

  defp get_interval_value(form) do
    # First try to get the value directly from the form's interval_value field
    case form["interval_value"] do
      value when is_binary(value) and value != "" ->
        case Integer.parse(value) do
          {int_val, ""} -> int_val
          # fallback to default
          _ -> 5
        end

      value when is_integer(value) ->
        value

      _ ->
        # Fallback: calculate from interval_ms if interval_value is not available
        interval_ms = String.to_integer(form["interval_ms"] || "5000")
        unit = form["interval_unit"] || "s"

        case unit do
          "h" -> div(interval_ms, 3_600_000)
          "m" -> div(interval_ms, 60_000)
          "s" -> div(interval_ms, 1000)
          "ms" -> interval_ms
        end
    end
  end

  defp convert_to_milliseconds(value, unit) do
    case unit do
      "ms" -> value
      "s" -> value * 1000
      "m" -> value * 60_000
      "h" -> value * 3_600_000
      _ -> value * 1000
    end
  end

  # Helper function to normalize scheduled message data (convert string keys to atom keys)
  defp normalize_scheduled_message(scheduled_msg) when is_map(scheduled_msg) do
    # Convert string keys to atom keys if needed
    if Map.has_key?(scheduled_msg, "topic") do
      # Has string keys, convert to atom keys
      for {key, val} <- scheduled_msg, into: %{}, do: {String.to_atom(key), val}
    else
      # Already has atom keys or is empty
      scheduled_msg
    end
  end

  defp normalize_scheduled_message(scheduled_msg), do: scheduled_msg

  defp get_mqtt5_properties_collapsed_state(broker_name) do
    if broker_name do
      ui_state = Mqttable.ConnectionSets.get_ui_state()
      mqtt5_collapsed_states = Map.get(ui_state, :mqtt5_properties_collapsed, %{})
      Map.get(mqtt5_collapsed_states, broker_name, false)
    else
      false
    end
  end

  defp parse_user_properties_from_params(params, current_properties) do
    # Extract user property fields from params and update current properties
    params
    |> Enum.filter(fn {key, _value} ->
      key_str = to_string(key)
      String.starts_with?(key_str, "user_property_")
    end)
    |> Enum.reduce(current_properties, fn {param_key, value}, acc ->
      case extract_index_and_field(to_string(param_key)) do
        {index, field} when index < length(acc) ->
          List.update_at(acc, index, fn property ->
            Map.put(property, field, value)
          end)

        _ ->
          acc
      end
    end)
  end

  defp extract_index_and_field(param_key) do
    # Extract index and field from keys like "user_property_key_0" or "user_property_value_0"
    case String.split(param_key, "_") do
      ["user", "property", field, index_str] ->
        case Integer.parse(index_str) do
          {index, ""} -> {index, field}
          _ -> nil
        end

      _ ->
        nil
    end
  end

  defp parse_integer_param(value, default) when is_binary(value) do
    case Integer.parse(value) do
      {int_val, ""} -> int_val
      _ -> default
    end
  end

  defp parse_integer_param(value, _default) when is_integer(value), do: value
  defp parse_integer_param(_, default), do: default

  defp extract_user_properties_from_params(params) do
    # Extract user properties from form params
    params
    |> Enum.filter(fn {key, _value} ->
      key_str = to_string(key)
      String.starts_with?(key_str, "user_property_")
    end)
    |> Enum.group_by(fn {key, _value} ->
      # Extract index from key like "user_property_key_0" or "user_property_value_0"
      key_str = to_string(key)

      key_str
      |> String.split("_")
      |> List.last()
      |> String.to_integer()
    end)
    |> Enum.sort_by(fn {index, _} -> index end)
    |> Enum.map(fn {_index, properties} ->
      # Convert list of key-value pairs to a map
      Enum.reduce(properties, %{"key" => "", "value" => ""}, fn {param_key, value}, acc ->
        param_key_str = to_string(param_key)

        if String.contains?(param_key_str, "_key_") do
          Map.put(acc, "key", value)
        else
          Map.put(acc, "value", value)
        end
      end)
    end)
    |> Enum.filter(fn %{"key" => key, "value" => value} ->
      key != "" && value != ""
    end)
  end

  # Payload validation functions (copied from send_message_modal_component.ex)

  defp validate_payload_in_form(form) do
    payload = Map.get(form, "payload", "")
    format = Map.get(form, "payload_format", "text")

    case validate_payload(payload, format) do
      {:ok, _} ->
        Map.put(form, "payload_validation_error", nil)

      {:error, error_message} ->
        Map.put(form, "payload_validation_error", error_message)
    end
  end

  defp validate_payload(payload, format) do
    case format do
      "json" -> validate_json_payload(payload)
      "hex" -> validate_hex_payload(payload)
      "file" -> validate_file_payload(payload)
      "text" -> {:ok, payload}
      _ -> {:ok, payload}
    end
  end

  defp validate_file_payload(""),
    do: {:error, "Please upload a file or select a different format"}

  defp validate_file_payload(payload) when is_binary(payload) do
    # Check if payload looks like base64 (basic validation)
    if String.length(payload) > 0 and String.match?(payload, ~r/^[A-Za-z0-9+\/]*={0,2}$/) do
      case Base.decode64(payload) do
        {:ok, _decoded} -> {:ok, payload}
        :error -> {:error, "Invalid file content. Please upload the file again."}
      end
    else
      {:error, "Please upload a file or select a different format"}
    end
  end

  defp validate_payload_in_form_after_template(form, final_payload, payload_format) do
    case validate_payload(final_payload, payload_format) do
      {:ok, _} ->
        Map.put(form, "payload_validation_error", nil)

      {:error, error_message} ->
        Map.put(form, "payload_validation_error", error_message)
    end
  end

  defp process_payload(payload, broker_name) do
    # Check if payload contains template syntax and render if needed
    if has_template_syntax?(payload) do
      # Get broker variables if broker_name is provided
      variables = get_broker_variables(broker_name)

      case Mqttable.Templating.Engine.render(payload, %{}, variables) do
        {:ok, rendered_payload} ->
          {rendered_payload, nil}

        {:error, reason} ->
          {payload, "Template error: #{reason}"}
      end
    else
      # Use payload as-is for plain text
      {payload, nil}
    end
  end

  defp has_template_syntax?(content) when is_binary(content) do
    String.contains?(content, "{{") || String.contains?(content, "{%")
  end

  defp has_template_syntax?(_), do: false

  defp get_broker_variables(broker_name) when is_binary(broker_name) and broker_name != "" do
    # Get all connection sets
    connection_sets = Mqttable.ConnectionSets.get_all()

    # Find the broker by name
    broker =
      Enum.find(connection_sets, fn set ->
        Map.get(set, :name) == broker_name
      end)

    case broker do
      nil ->
        %{}

      broker ->
        # Extract enabled variables and convert to map
        broker
        |> Map.get(:variables, [])
        |> Enum.filter(fn var -> Map.get(var, :enabled, true) end)
        |> Enum.reduce(%{}, fn var, acc ->
          name = Map.get(var, :name)
          value = Map.get(var, :value, "")

          if name && name != "" do
            Map.put(acc, name, value)
          else
            acc
          end
        end)
    end
  end

  defp get_broker_variables(_broker_name) do
    %{}
  end

  defp extract_payload_format(params, form) do
    # First try to get from payload_format field (from component state)
    case Map.get(params, "payload_format") do
      nil ->
        # If not found, try to extract from format-scheduled-X field (from radio buttons)
        format_key = Enum.find(Map.keys(params), &String.starts_with?(&1, "format-scheduled-"))

        case format_key do
          nil ->
            # Fall back to form state or default
            Map.get(form, "payload_format", "text")

          key ->
            Map.get(params, key, "text")
        end

      format ->
        format
    end
  end

  defp validate_json_payload(""), do: {:ok, ""}

  defp validate_json_payload(payload) when is_binary(payload) do
    case Jason.decode(payload) do
      {:ok, _} -> {:ok, payload}
      {:error, _} -> {:error, "Invalid JSON format"}
    end
  end

  defp validate_hex_payload(""), do: {:ok, ""}

  defp validate_hex_payload(payload) when is_binary(payload) do
    # Remove whitespace and check for valid hex
    cleaned = String.replace(payload, ~r/\s/, "")

    cond do
      cleaned == "" ->
        {:ok, payload}

      not String.match?(cleaned, ~r/^[0-9A-Fa-f]*$/) ->
        {:error, "Invalid hex format. Use only 0-9, A-F characters"}

      rem(String.length(cleaned), 2) != 0 ->
        {:error, "Hex payload must have even number of characters"}

      true ->
        {:ok, payload}
    end
  end

  # Add RFC3339 timestamp to alert message
  defp add_timestamp_to_message(message) do
    timestamp = DateTime.utc_now() |> DateTime.to_iso8601()
    "[#{timestamp}] #{message}"
  end
end
